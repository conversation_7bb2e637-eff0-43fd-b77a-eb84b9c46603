# Bot Creator

Un système de bot principal qui peut créer et gérer d'autres bots automatisés.

## Fonctionnalités

- **Bot Principal** : Gère la création et la supervision d'autres bots
- **Gestion Multi-Bots** : <PERSON><PERSON><PERSON>, configure et contrôle plusieurs bots simultanément
- **APIs Intégrées** :
  - Check Status API
  - Spam API  
  - Events API
  - Info API
- **Interface de Configuration** : Configuration facile des nouveaux bots
- **Monitoring** : Surveillance en temps réel des bots actifs

## Structure du Projet

```
├── src/
│   ├── main.js              # Point d'entrée principal
│   ├── bot-manager.js       # Gestionnaire de bots
│   ├── apis/               # Modules API
│   │   ├── check-status.js
│   │   ├── spam.js
│   │   ├── events.js
│   │   └── info.js
│   ├── bots/               # Bots créés dynamiquement
│   └── config/             # Configurations
├── data/                   # Données des bots
└── logs/                   # Logs du système
```

## Installation

```bash
npm install
```

## Utilisation

```bash
# Démarrer le bot principal
npm start

# Mode développement
npm run dev
```

## APIs Utilisées

1. **Check Status**: `https://ch9ayfa-check-1.vercel.app/check_status?key=ch9ayfa&uid={uid}`
2. **Spam**: `https://spam-ch9ayfa.vercel.app/spam?id={uid}`
3. **Events**: `https://eventes-ch9ayfa.vercel.app/eventes?region=ME&key=ch9ayfa`
4. **Info**: `https://info-ch9ayfa.vercel.app/2511293320`
